/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color System - Semantic Colors */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    --secondary-50: #fffbeb;
    --secondary-100: #fef3c7;
    --secondary-200: #fde68a;
    --secondary-300: #fcd34d;
    --secondary-400: #fbbf24;
    --secondary-500: #f59e0b;
    --secondary-600: #d97706;
    --secondary-700: #b45309;
    --secondary-800: #92400e;
    --secondary-900: #78350f;

    --neutral-50: #f9fafb;
    --neutral-100: #f3f4f6;
    --neutral-200: #e5e7eb;
    --neutral-300: #d1d5db;
    --neutral-400: #9ca3af;
    --neutral-500: #6b7280;
    --neutral-600: #4b5563;
    --neutral-700: #374151;
    --neutral-800: #1f2937;
    --neutral-900: #111827;

    /* Semantic Color Assignments */
    --primary-color: var(--primary-600);
    --primary-light: var(--primary-500);
    --primary-dark: var(--primary-700);
    --secondary-color: var(--secondary-500);
    --accent-color: var(--neutral-700);
    --text-primary: var(--neutral-900);
    --text-secondary: var(--neutral-600);
    --text-muted: var(--neutral-500);
    --background-primary: var(--neutral-50);
    --background-secondary: #ffffff;
    --border-light: var(--neutral-200);
    --border-medium: var(--neutral-300);

    /* Typography Scale */
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 1.875rem;  /* 30px */
    --font-size-4xl: 2.25rem;   /* 36px */
    --font-size-5xl: 3rem;      /* 48px */
    --font-size-6xl: 3.75rem;   /* 60px */

    /* Line Heights */
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;

    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;

    /* Spacing Scale (8px base) */
    --space-1: 0.25rem;   /* 4px */
    --space-2: 0.5rem;    /* 8px */
    --space-3: 0.75rem;   /* 12px */
    --space-4: 1rem;      /* 16px */
    --space-5: 1.25rem;   /* 20px */
    --space-6: 1.5rem;    /* 24px */
    --space-8: 2rem;      /* 32px */
    --space-10: 2.5rem;   /* 40px */
    --space-12: 3rem;     /* 48px */
    --space-16: 4rem;     /* 64px */
    --space-20: 5rem;     /* 80px */
    --space-24: 6rem;     /* 96px */
    --space-32: 8rem;     /* 128px */

    /* Border Radius */
    --radius-sm: 0.25rem;   /* 4px */
    --radius-md: 0.5rem;    /* 8px */
    --radius-lg: 0.75rem;   /* 12px */
    --radius-xl: 1rem;      /* 16px */
    --radius-2xl: 1.5rem;   /* 24px */
    --radius-full: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Transitions */
    --transition-fast: 150ms ease;
    --transition-base: 250ms ease;
    --transition-slow: 350ms ease;

    /* Z-index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;

    /* Legacy Support */
    --white: var(--background-secondary);
    --light-gray: var(--background-primary);
    --text-dark: var(--text-primary);
    --text-light: var(--text-secondary);
    --border-color: var(--border-light);
    --shadow: var(--shadow-md);
    --shadow-hover: var(--shadow-xl);
    --gradient: linear-gradient(135deg, var(--primary-color), var(--accent-color));
}

/* Base Typography & Layout */
body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: var(--font-size-base);
    line-height: var(--leading-normal);
    color: var(--text-primary);
    background-color: var(--background-secondary);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography Hierarchy */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-bold);
    line-height: var(--leading-tight);
    color: var(--text-primary);
    margin: 0;
}

h1 {
    font-size: var(--font-size-5xl);
    font-weight: var(--font-weight-extrabold);
    letter-spacing: -0.025em;
}

h2 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    letter-spacing: -0.025em;
}

h3 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
}

h4 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
}

h5 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
}

h6 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
}

p {
    margin: 0 0 var(--space-4) 0;
    color: var(--text-secondary);
    line-height: var(--leading-relaxed);
}

.text-large {
    font-size: var(--font-size-lg);
    line-height: var(--leading-relaxed);
}

.text-small {
    font-size: var(--font-size-sm);
}

.text-xs {
    font-size: var(--font-size-xs);
}

.text-muted {
    color: var(--text-muted);
}

.text-center {
    text-align: center;
}

/* Layout Containers */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-5);
}

.container-sm {
    max-width: 640px;
    margin: 0 auto;
    padding: 0 var(--space-5);
}

.container-lg {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-5);
}

/* Focus Management */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

*:focus:not(:focus-visible) {
    outline: none;
}

*:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Skip Link for Accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: var(--space-6);
    background: var(--primary-color);
    color: white;
    padding: var(--space-2) var(--space-4);
    text-decoration: none;
    border-radius: var(--radius-md);
    z-index: var(--z-tooltip);
    transition: top var(--transition-fast);
}

.skip-link:focus {
    top: var(--space-4);
}

/* Navigation - Enhanced UX */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border-bottom: 1px solid var(--border-light);
    z-index: var(--z-fixed);
    padding: var(--space-4) 0;
    transition: all var(--transition-base);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-sm);
    padding: var(--space-3) 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-5);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    text-decoration: none;
    transition: transform var(--transition-fast);
}

.nav-logo:hover {
    transform: scale(1.05);
}

.nav-logo i {
    margin-right: var(--space-2);
    font-size: var(--font-size-2xl);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--space-8);
    margin: 0;
    padding: 0;
}

.nav-menu li {
    position: relative;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    position: relative;
    display: block;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: all var(--transition-fast);
    transform: translateX(-50%);
}

.nav-menu a:hover,
.nav-menu a:focus {
    color: var(--primary-color);
    background: var(--primary-50);
}

.nav-menu a:hover::after,
.nav-menu a:focus::after {
    width: 80%;
}

.nav-menu a.active {
    color: var(--primary-color);
    background: var(--primary-50);
}

.nav-menu a.active::after {
    width: 80%;
}

/* Mobile Navigation Toggle */
.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: var(--space-2);
    border: none;
    background: none;
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}

.nav-toggle:hover {
    background: var(--neutral-100);
}

.nav-toggle:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.bar {
    width: 24px;
    height: 2px;
    background: var(--text-primary);
    margin: 2px 0;
    transition: all var(--transition-base);
    border-radius: 2px;
}

.nav-toggle.active .bar:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
}

.nav-toggle.active .bar:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* CTA Button in Navigation */
.nav-cta {
    display: none;
    margin-left: var(--space-4);
}

.nav-cta .btn {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-sm);
}

@media (min-width: 1024px) {
    .nav-cta {
        display: block;
    }
}

/* Hero Section */
.hero {
    min-height: 100vh;
    background: linear-gradient(rgba(44, 90, 160, 0.8), rgba(52, 73, 94, 0.8)),
                url('https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=1920&h=1080&fit=crop') center/cover;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white);
    position: relative;
}

.hero-content {
    max-width: 800px;
    margin-bottom: 3rem;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    animation: fadeInUp 1s ease;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease 0.2s both;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease 0.4s both;
}

/* Enhanced Button System */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    border: 2px solid transparent;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    line-height: 1;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    user-select: none;
    -webkit-user-select: none;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Button Sizes */
.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-xs);
}

.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: var(--font-size-base);
}

.btn-xl {
    padding: var(--space-5) var(--space-10);
    font-size: var(--font-size-lg);
}

/* Primary Button */
.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
}

/* Secondary Button */
.btn-secondary {
    background: var(--secondary-color);
    color: white;
    border-color: var(--secondary-color);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--secondary-600);
    border-color: var(--secondary-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* Outline Button */
.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* Ghost Button */
.btn-ghost {
    background: transparent;
    color: var(--text-primary);
    border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
    background: var(--neutral-100);
    color: var(--primary-color);
}

/* White Button (for dark backgrounds) */
.btn-white {
    background: white;
    color: var(--primary-color);
    border-color: white;
}

.btn-white:hover:not(:disabled) {
    background: var(--neutral-100);
    border-color: var(--neutral-100);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* Full Width Button */
.btn-full {
    width: 100%;
}

/* Loading State */
.btn-loading {
    position: relative;
    color: transparent;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Icon Buttons */
.btn-icon {
    padding: var(--space-3);
    border-radius: var(--radius-full);
}

.btn-icon-sm {
    padding: var(--space-2);
}

.btn-icon-lg {
    padding: var(--space-4);
}

/* Button Groups */
.btn-group {
    display: inline-flex;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.btn-group .btn {
    border-radius: 0;
    border-right-width: 1px;
}

.btn-group .btn:first-child {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
}

.btn-group .btn:last-child {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
    border-right-width: 2px;
}

/* Legacy Support */
.btn-search {
    background: var(--primary-color);
    color: white;
    width: 100%;
}

.btn-search:hover:not(:disabled) {
    background: var(--primary-dark);
}

/* Hero Search */
.hero-search {
    position: absolute;
    bottom: -100px;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 1000px;
}

.search-container {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: var(--shadow-hover);
}

.search-container h3 {
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    text-align: center;
}

.search-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.search-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.search-field {
    display: flex;
    flex-direction: column;
}

.search-field label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.search-field input,
.search-field select {
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.search-field input:focus,
.search-field select:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Stats Section */
.stats {
    padding: 150px 0 80px;
    background: var(--light-gray);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    text-align: center;
}

.stat-item {
    padding: 2rem;
    background: var(--white);
    border-radius: 15px;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    color: var(--text-light);
    font-weight: 500;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

/* Featured Properties */
.featured-properties {
    padding: 80px 0;
    background: var(--white);
}

.properties-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.property-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.property-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.property-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.property-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.property-card:hover .property-image img {
    transform: scale(1.1);
}

.property-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--secondary-color);
    color: var(--white);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.property-price {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.8);
    color: var(--white);
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 1.1rem;
    font-weight: 700;
}

.property-info {
    padding: 1.5rem;
}

.property-info h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.property-location {
    color: var(--text-light);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.property-location i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.property-features {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.property-features span {
    display: flex;
    align-items: center;
    color: var(--text-light);
    font-size: 0.9rem;
}

.property-features i {
    margin-right: 0.3rem;
    color: var(--primary-color);
}

.view-all {
    text-align: center;
}

/* Services Section */
.services {
    padding: 80px 0;
    background: var(--light-gray);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.service-icon i {
    font-size: 2rem;
    color: var(--white);
}

.service-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.service-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* About Section */
.about {
    padding: 80px 0;
    background: var(--white);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text .section-header {
    text-align: left;
    margin-bottom: 2rem;
}

.about-description {
    margin-bottom: 2rem;
}

.about-description p {
    margin-bottom: 1rem;
    color: var(--text-light);
    line-height: 1.8;
}

.about-features {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.feature-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-top: 0.2rem;
}

.feature-item h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.feature-item p {
    color: var(--text-light);
    font-size: 0.95rem;
}

.about-image {
    position: relative;
}

.about-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 15px;
    box-shadow: var(--shadow);
}

/* Testimonials Section */
.testimonials {
    padding: 80px 0;
    background: var(--light-gray);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
}

.testimonial-content {
    margin-bottom: 1.5rem;
}

.stars {
    display: flex;
    gap: 0.2rem;
    margin-bottom: 1rem;
}

.stars i {
    color: var(--secondary-color);
    font-size: 1.1rem;
}

.testimonial-content p {
    color: var(--text-light);
    line-height: 1.6;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.testimonial-author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.testimonial-author h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.2rem;
}

.testimonial-author span {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* CTA Section */
.cta {
    padding: 80px 0;
    background: var(--gradient);
    color: var(--white);
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.cta .btn-secondary {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

.cta .btn-secondary:hover {
    background: var(--white);
    color: var(--primary-color);
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: var(--white);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-icon i {
    color: var(--white);
    font-size: 1.2rem;
}

.contact-item h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.contact-item p {
    color: var(--text-light);
    line-height: 1.6;
}

.contact-form {
    background: var(--light-gray);
    padding: 2rem;
    border-radius: 15px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background: var(--text-dark);
    color: var(--white);
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.footer-logo i {
    margin-right: 0.5rem;
    color: var(--secondary-color);
}

.footer-section p {
    color: #bdc3c7;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-section h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--white);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--secondary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid #34495e;
    padding-top: 2rem;
    text-align: center;
    color: #bdc3c7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--white);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .search-row {
        grid-template-columns: 1fr;
    }

    .hero-search {
        position: static;
        transform: none;
        margin-top: 2rem;
        width: 95%;
    }

    .stats {
        padding: 80px 0;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .properties-grid {
        grid-template-columns: 1fr;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-text .section-header {
        text-align: center;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}
