@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-white hover:bg-gray-50 text-primary-600 font-semibold py-3 px-6 rounded-lg border-2 border-primary-600 transition-all duration-200;
  }

  .section-padding {
    @apply py-16 lg:py-24;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .gradient-bg {
    @apply bg-gradient-to-br from-primary-50 via-white to-gold-50;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-2xl hover:-translate-y-2;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-gold-600 bg-clip-text text-transparent;
  }

  .hero-pattern {
    background-image:
      radial-gradient(circle at 25% 25%, rgba(14, 165, 233, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
  }

  .property-card {
    @apply bg-white rounded-xl shadow-lg overflow-hidden card-hover;
  }

  .feature-icon {
    @apply w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600;
  }
}
